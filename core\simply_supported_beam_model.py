import openseespy.opensees as ops
from core.model import BridgeModel
from params import BridgeParams
from materials.concrete import set_concrete
from materials.steel import set_steel
from materials.rubber import set_rubber
from materials.soil import set_backfill, set_pile_abutment, set_pile_pier
from materials.rigid_link import set_rigid_link
from sections.box_girder import set_box_girder_section
from sections.circular_pier import set_circular_pier_section
from sections.hollow_slab import set_hollow_slab_section
from sections.rectangular_cap_beam import set_rectangular_cap_beam_section

# Import component modules
from components.simply_supported_beam.deck import create_deck
from components.simply_supported_beam.pier import create_piers
from components.simply_supported_beam.isolator import create_isolators
from components.simply_supported_beam.boundary import apply_boundary_conditions
from components.simply_supported_beam.load import apply_loads


class SimplySupportedBeamModel(BridgeModel):
    """
    A simply supported beam bridge model with discontinuous spans.

    Features:
    - Discontinuous beams at each pier
    - Cap beams rigidly connected to piers
    - Rubber bearings connecting cap beams to main beams
    - Multiple piers in the transverse direction
    - Multiple bearings on each cap beam
    - Support for cases without piers
    """

    def __init__(self, params: BridgeParams):
        super().__init__(params)

        self._init_model()
        self._build()
        self._apply_loads()

    def _build(self):
        self._create_materials()
        self._create_sections()
        create_deck(self)
        create_piers(self)
        create_isolators(self)
        apply_boundary_conditions(self)


    def _create_materials(self):
        self.mat_tags = {
            'ConcreteCover':     self._next_tag('material'),  # 保护层混凝土（非约束）
            'ConcreteCore':      self._next_tag('material'),  # 核心区混凝土（约束）
            'SteelLongitudinal': self._next_tag('material'),  # 纵向钢筋
            'SteelTransverse':   self._next_tag('material'),  # 横向箍筋
            'RubberX':           self._next_tag('material'),  # 橡胶支座水平剪切
            'RubberZ':           self._next_tag('material'),  # 橡胶支座竖直受压
            'RubberRxy':         self._next_tag('material'),  # 橡胶支座弯曲
            'RubberRz':          self._next_tag('material'),  # 橡胶支座转动
            'BackfillLeft':      self._next_tag('material'),  # 左桥台填土
            'BackfillRight':     self._next_tag('material'),  # 右桥台填土
            'PileAbutment':      self._next_tag('material'),  # 桥台桩土相互作用
            'PilePierColumn':    self._next_tag('material'),  # 桥墩桩土相互作用 (单根墩柱)
            'RigidLink':         self._next_tag('material'),  # 刚性连接（测试）
            'CollisionLeft':     self._next_tag('material'),  # 主梁-左桥台碰撞
            'CollisionRight':    self._next_tag('material'),  # 主梁-右桥台碰撞
            'CollisionGirder':   self._next_tag('material'),  # 主梁-主梁纵向碰撞
            'RestoringLeft':     self._next_tag('material'),  # 左桥台恢复弹簧
            'RestoringRight':    self._next_tag('material'),  # 右桥台恢复弹簧
            'RestoringGirder':   self._next_tag('material'),  # 主梁间恢复弹簧
        }
        set_concrete(self.params, self.mat_tags)
        set_steel(self.params, self.mat_tags)
        set_rubber(self.params, self.mat_tags)
        set_backfill(self.params, self.mat_tags)
        set_rigid_link(self.params, self.mat_tags)
        if self.params.pile_soil["enable_ssi"]:
            set_pile_abutment(self.params, self.mat_tags)
            set_pile_pier(self.params, self.mat_tags)

    def _create_sections(self):
        """创建主梁、盖梁和桥墩的纤维截面"""
        # 根据截面类型创建主梁截面
        girder_type = self.params.girder

        if girder_type == 'hollow_slab':
            # 空心板梁截面
            self.sections['Deck'] = set_hollow_slab_section(
                section_tag=len(self.sections) + 1,
                params=self.params
            )
        elif girder_type == 'box':
            # 箱型截面
            self.sections['Deck'] = set_box_girder_section(
                section_tag=len(self.sections) + 1,
                params=self.params
            )
        else:
            raise ValueError(f"不支持的梁截面类型: {girder_type}")

        # 桥墩圆形截面
        self.sections['Pier'] = set_circular_pier_section(
            section_tag=len(self.sections) + 1,
            params=self.params,
            mat_tags=self.mat_tags
        )

        # 盖梁矩形截面
        if self.params.num_spans > 1:
            self.sections['Cap'] = set_rectangular_cap_beam_section(
                section_tag=len(self.sections) + 1,
                params=self.params,
                mat_tags=self.mat_tags
            )


    # Apply loads
    def _apply_loads(self):
        apply_loads(self)