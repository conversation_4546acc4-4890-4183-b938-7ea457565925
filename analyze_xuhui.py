#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
该脚本读取地震动场数据文件, 自动执行时程分析并保存桥梁响应数据
输入文件为numpy数组, shape为(n, T), n为地震动数量, T为时间维度
输出为npz文件, 包含桥墩位移、支座位移和支座剪力数据
"""

import os
import numpy as np
from typing import Dict, Tuple
from dataclasses import dataclass

# 导入桥梁模型和分析器
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel
from analysis.enhanced_analyzer import EnhancedBridgeAnalyzer


@dataclass
class AnalysisConfig:
    """分析配置类"""
    save_temp_files: bool = False  # 是否保存临时文件
    temp_file_dir: str = "temp_results"  # 临时文件目录
    cleanup_temp_files: bool = True  # 分析完成后是否清理临时文件

    @classmethod
    def fast_mode(cls):
        """快速模式：跳过所有文件操作"""
        return cls(
            save_temp_files=False,
            cleanup_temp_files=True
        )

    @classmethod
    def debug_mode(cls):
        """调试模式：保存临时文件"""
        return cls(
            save_temp_files=True,
            cleanup_temp_files=False
        )


class ResponseDataCollector:
    """响应数据收集器"""

    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.pier_responses = {}
        self.bearing_responses = {}
        self.temp_files = []  # 记录创建的临时文件

    def collect_pier_response(self, pier_key: Tuple, time_data: np.ndarray,
                            disp_data: Dict[str, np.ndarray]) -> None:
        """收集桥墩响应数据"""
        self.pier_responses[pier_key] = {
            'time': time_data,
            'disp_x': disp_data.get('disp_x', np.array([])),
            'disp_y': disp_data.get('disp_y', np.array([])),
            'disp_z': disp_data.get('disp_z', np.array([]))
        }

    def collect_bearing_response(self, bearing_idx: int, time_data: np.ndarray,
                                force_data: Dict[str, np.ndarray],
                                deform_data: Dict[str, np.ndarray],
                                coords: Dict[str, float]) -> None:
        """收集支座响应数据"""
        self.bearing_responses[bearing_idx] = {
            'x_coord': coords['x_coord'],
            'y_coord': coords['y_coord'],
            'force_data': {
                'time': time_data,
                'force_x': force_data.get('force_x', np.array([])),
                'force_y': force_data.get('force_y', np.array([])),
                'force_z': force_data.get('force_z', np.array([]))
            },
            'deform_data': {
                'time': time_data,
                'deform_x': deform_data.get('deform_x', np.array([])),
                'deform_y': deform_data.get('deform_y', np.array([])),
                'deform_z': deform_data.get('deform_z', np.array([]))
            }
        }

    def get_pier_data(self) -> Dict:
        """获取桥墩数据"""
        return self.pier_responses.copy()

    def get_bearing_data(self) -> Dict:
        """获取支座数据"""
        return self.bearing_responses.copy()

    def clear(self) -> None:
        """清空收集的数据"""
        self.pier_responses.clear()
        self.bearing_responses.clear()

    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        if self.config.cleanup_temp_files:
            for file_path in self.temp_files:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    print(f"清理临时文件 {file_path} 时出错: {e}")
            self.temp_files.clear()


def run_analysis(
    params: BridgeParams,
    ground_motion, dt=0.01, direction=1, pga=0.15,
    config: AnalysisConfig = None) -> Tuple[Dict, Dict, Dict]:
    """
    执行单个地震动的分析

    参数:
        params: 桥梁参数对象
        ground_motion: 地震记录数据
        dt: 时间步长
        direction: 地震方向 (1=纵向, 2=横向, 3=竖向)
        pga: 峰值地面加速度
        config: 分析配置对象

    返回:
        pier_data: 桥墩位移数据字典
        bearing_data: 支座响应数据字典
        analysis_stats: 分析统计信息
    """
    # 使用默认配置如果未提供
    if config is None:
        config = AnalysisConfig()

    # 创建响应数据收集器
    collector = ResponseDataCollector(config)

    # 创建桥梁模型
    model = SimplySupportedBeamModel(params)

    # 分析器
    analyzer = EnhancedBridgeAnalyzer(model)
    analyzer.set_response_collector(collector)

    analyzer.direction = direction

    # 运行分析
    analyzer.modal()
    analysis_stats = analyzer.dynamic(h=ground_motion, pga=pga, dt=dt)

    # 获取响应数据
    pier_data = collector.get_pier_data()
    bearing_data = collector.get_bearing_data()

    # 清理临时文件
    collector.cleanup_temp_files()

    return pier_data, bearing_data, analysis_stats


def process_analysis_results(pier_data: Dict, bearing_data: Dict, n_steps: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    处理分析结果，提取代表性数据并格式化为数组

    参数:
        pier_data: 桥墩位移数据
        bearing_data: 支座响应数据
        n_steps: 时间步数

    返回:
        pier_displacements: 桥墩位移数组
        bearing_displacements: 支座位移数组
        bearing_forces: 支座剪力数组
    """
    # 获取代表性桥墩数据
    pier_keys = sorted(pier_data.keys()) if pier_data else []
    n_piers = len(pier_keys)

    # 处理单跨结构（无桥墩）的情况
    if n_piers == 0:
        pier_displacements = np.zeros((0, n_steps))  # 空数组，维度正确
    else:
        pier_displacements = np.zeros((n_piers, n_steps))

        for i, pier_key in enumerate(pier_keys):
            if pier_key in pier_data:
                disp_x = pier_data[pier_key]['disp_x']
                # 确保长度匹配
                if len(disp_x) > n_steps:
                    disp_x = disp_x[:n_steps]
                elif len(disp_x) < n_steps:
                    temp = np.zeros(n_steps)
                    temp[:len(disp_x)] = disp_x
                    disp_x = temp
                pier_displacements[i, :] = disp_x

    # 获取代表性支座数据
    bearing_indices = sorted(bearing_data.keys()) if bearing_data else []
    n_bearings = len(bearing_indices)

    if n_bearings == 0:
        # 如果没有支座数据，返回空数组
        bearing_displacements = np.zeros((0, n_steps))
        bearing_forces = np.zeros((0, n_steps))
        print("警告: 没有支座数据")
    else:
        bearing_displacements = np.zeros((n_bearings, n_steps))
        bearing_forces = np.zeros((n_bearings, n_steps))

        for i, bearing_idx in enumerate(bearing_indices):
            if bearing_idx in bearing_data:
                # 支座相对位移（X方向）
                deform_x = bearing_data[bearing_idx]['deform_data']['deform_x']
                # 支座剪力（X方向）
                force_x = bearing_data[bearing_idx]['force_data']['force_x']

                # 确保长度匹配
                if len(deform_x) > n_steps:
                    deform_x = deform_x[:n_steps]
                    force_x = force_x[:n_steps]
                elif len(deform_x) < n_steps:
                    temp_deform = np.zeros(n_steps)
                    temp_force = np.zeros(n_steps)
                    temp_deform[:len(deform_x)] = deform_x
                    temp_force[:len(force_x)] = force_x
                    deform_x = temp_deform
                    force_x = temp_force

                bearing_displacements[i, :] = deform_x
                bearing_forces[i, :] = force_x

    return pier_displacements, bearing_displacements, bearing_forces


def main(
    config_file,
    ground_motion, 
    output_path = 'out',
    save_temp_files: bool = False,
):
    """
    执行地震动时程分析，自动选择代表性桥墩和支座进行记录，
    并将结果保存为npz格式文件。

    参数:
        save_temp_files: 是否保存临时文件
    """
    # 分析参数设置
    dt = 0.02 # 时间步长 (s)
    pga = np.abs(ground_motion).max() / 9.807 # 峰值地面加速度 (g)

    # 设置输出路径
    output_file = os.path.basename(config_file).replace('.json', '.npz')
    output_file = os.path.join(output_path, output_file)
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    # 创建分析配置
    if  not save_temp_files:
        analysis_config = AnalysisConfig.fast_mode()
        print("分析配置: 快速模式（跳过文件操作）")
    elif save_temp_files:
        analysis_config = AnalysisConfig.debug_mode()
        print("分析配置: 调试模式（保存临时文件）")

    # 读取模型参数
    try:
        params = BridgeParams(config_file)
        print(f"成功读取桥梁配置: {config_file}")
    except Exception as e:
        print(f"读取桥梁配置文件时出错: {e}")
        return

    # 地震动
    n_motions = 1
    n_steps = ground_motion.shape[0]
    print(f"地震动时间步数: {n_steps}")
    
    # 检查是否存在已有结果
    if os.path.exists(output_file):
        print("已计算, 跳过分析")
        return

    # 对单个地震动执行分析
    pier_data, bearing_data, _ = run_analysis(
        params, ground_motion, pga=pga, dt=dt, config=analysis_config
    )

    # 处理分析结果
    pier_disps, bearing_disps, bearing_forces = process_analysis_results(
        pier_data, bearing_data, n_steps
    )

    # 初始化数组
    n_piers = pier_disps.shape[0]
    n_bearings = bearing_disps.shape[0]

    # 处理单跨结构（无桥墩）的情况
    if n_piers == 0:
        print("检测到单跨结构（无桥墩），仅记录支座响应")
        pier_displacements_all = np.zeros((1, n_motions, n_steps))  # 保持维度一致性，使用占位符
    else:
        pier_displacements_all = np.zeros((n_piers, n_motions, n_steps))

    bearing_displacements_all = np.zeros((n_bearings, n_motions, n_steps))
    bearing_forces_all = np.zeros((n_bearings, n_motions, n_steps))

    # 存储当前地震动的结果
    if pier_disps.shape[0] > 0:  # 有桥墩数据时才存储
        pier_displacements_all[:pier_disps.shape[0], 0, :] = pier_disps
    bearing_displacements_all[:, 0, :] = bearing_disps
    bearing_forces_all[:, 0, :] = bearing_forces

    # 保存中间结果
    save_results(
        output_file,
        pier_displacements_all,
        bearing_displacements_all,
        bearing_forces_all,
        pier_data,
        bearing_data
    )

    print(f"\n分析完成! 结果已保存至: {output_file}")
    print_analysis_summary(output_file)


def save_results(output_file: str, pier_displacements: np.ndarray,
                bearing_displacements: np.ndarray, bearing_forces: np.ndarray,
                pier_data: Dict, bearing_data: Dict):
    """
    保存分析结果为npz格式

    参数:
        output_file: 输出文件路径
        pier_displacements: 桥墩位移数组
        bearing_displacements: 支座位移数组
        bearing_forces: 支座剪力数组
        pier_data: 桥墩数据字典（用于保存元数据）
        bearing_data: 支座数据字典（用于保存元数据）
    """
    try:
        # 准备元数据
        pier_keys = list(pier_data.keys()) if pier_data else []
        bearing_indices = list(bearing_data.keys()) if bearing_data else []

        # 添加结构类型标识
        is_single_span = len(pier_keys) == 0

        # 保存为npz格式
        np.savez_compressed(
            output_file,
            pier_displacements=pier_displacements,
            bearing_displacements=bearing_displacements,
            bearing_forces=bearing_forces,
            pier_keys=pier_keys,
            bearing_indices=bearing_indices,
            is_single_span=is_single_span  # 添加结构类型标识
        )
    except Exception as e:
        print(f"保存结果文件时出错: {e}")
        raise


def print_analysis_summary(output_file: str):
    """
    打印分析结果摘要

    参数:
        output_file: 结果文件路径
    """
    try:
        if not os.path.exists(output_file):
            print("结果文件不存在，无法生成摘要")
            return

        data = np.load(output_file)

        print("\n=== 分析结果摘要 ===")

        # 检查是否为单跨结构
        is_single_span = data.get('is_single_span', False)
        if is_single_span:
            print("结构类型: 单跨结构（无桥墩）")
        else:
            print("结构类型: 多跨结构（含桥墩）")

        if 'pier_displacements' in data:
            pier_disps = data['pier_displacements']
            n_piers, n_motions, n_steps = pier_disps.shape

            # 对于单跨结构，实际桥墩数量为0
            actual_n_piers = 0 if is_single_span else n_piers
            print(f"桥墩数量: {actual_n_piers}")
            print(f"地震动数量: {n_motions}")
            print(f"时间步数: {n_steps}")

            # 只有在有桥墩时才计算最大位移
            if not is_single_span and n_piers > 0:
                max_pier_disp = np.max(np.abs(pier_disps))
                print(f"桥墩最大位移: {max_pier_disp:.6f} m")

        if 'bearing_displacements' in data:
            bearing_disps = data['bearing_displacements']
            n_bearings = bearing_disps.shape[0]
            print(f"支座数量: {n_bearings}")

            # 计算最大支座位移
            max_bearing_disp = np.max(np.abs(bearing_disps))
            print(f"支座最大位移: {max_bearing_disp:.6f} m")

        if 'bearing_forces' in data:
            bearing_forces = data['bearing_forces']

            # 计算最大支座剪力
            max_bearing_force = np.max(np.abs(bearing_forces))
            print(f"支座最大剪力: {max_bearing_force:.2f} N")

        print("=" * 25)

    except Exception as e:
        print(f"生成分析摘要时出错: {e}")


if __name__ == "__main__":
    """
    执行地震动时程分析，包括：
    1. 自动选择代表性桥墩和支座
    2. 记录桥墩位移、支座位移和支座剪力
    3. 将结果保存为npz格式文件

    支持命令行参数:
    --fast: 快速模式（默认）
    --debug: 调试模式（保存临时文件）
    """
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='桥梁地震响应分析脚本')
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument('--fast', action='store_true', default=True,
                            help='快速模式：跳过文件操作（默认）')
    mode_group.add_argument('--debug', action='store_true', default=False,
                            help='调试模式：保存临时文件')
    mode_group.add_argument('--gmf', default='gmf_changshu_M5.1.npy')

    args = parser.parse_args()
    print('Using ground motion field', args.gmf)
    
    # ground motions
    base = 'gm/gmf'
    output_path = os.path.join('out', args.gmf.split('.npy')[0].split('gmf_')[1])
    
    idx_analyze = np.loadtxt(os.path.join(base, 'bridge_idx.txt'))
    idx_analyze = [2]
    gmf = np.load(os.path.join(base, args.gmf), allow_pickle=True).item()
    idx_gm = gmf['i']
    histories = gmf['h']
    
    # bridges
    config_list = os.listdir('configs')
    
    for config in config_list:
        idx = int(config.split('-')[1])
        if idx not in idx_analyze:
            continue
        
        print(f"\n{'='*60}")
        print(f"开始分析桥梁: {config}")
        print(f"{'='*60}\n")
        
        config = os.path.join('configs', config)
        gm = histories[np.where(idx_gm == idx)[0][0]]
        
        # NTHA
        main(config, gm, output_path, save_temp_files=args.debug)
        
        print(f"\n{'*'*60}")
        print(f"完成分析: {os.path.basename(config)}")
        print(f"{'*'*60}\n")